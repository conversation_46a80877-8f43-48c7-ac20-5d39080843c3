{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\React Assignment\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error for this field when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n    return newErrors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const formErrors = validateForm();\n    if (Object.keys(formErrors).length > 0) {\n      setErrors(formErrors);\n      return;\n    }\n    setLoading(true);\n    setMessage('');\n    setErrors({});\n    try {\n      const result = await login(formData.email, formData.password);\n      if (result.success) {\n        setMessage('Login successful! Redirecting...');\n        setTimeout(() => {\n          navigate('/dashboard');\n        }, 1000);\n      } else {\n        setMessage(result.message);\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"login-title\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${message.includes('successful') ? 'success-message' : 'error-message'}`,\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: \"form-label\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            className: `form-input ${errors.email ? 'error' : ''}`,\n            placeholder: \"Enter your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            className: `form-input ${errors.password ? 'error' : ''}`,\n            placeholder: \"Enter your password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.password\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login-button\",\n          disabled: loading,\n          children: [loading && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 25\n          }, this), loading ? 'Logging in...' : 'Login']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        className: \"auth-link\",\n        children: \"Don't have an account? Register Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"P/rt9eC6aB/50uBCO+zYesD0gSo=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "errors", "setErrors", "loading", "setLoading", "message", "setMessage", "login", "navigate", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "handleSubmit", "preventDefault", "formErrors", "Object", "keys", "length", "result", "success", "setTimeout", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/React Assignment/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/Auth.css';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error for this field when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n\n    return newErrors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const formErrors = validateForm();\n    if (Object.keys(formErrors).length > 0) {\n      setErrors(formErrors);\n      return;\n    }\n\n    setLoading(true);\n    setMessage('');\n    setErrors({});\n\n    try {\n      const result = await login(formData.email, formData.password);\n      \n      if (result.success) {\n        setMessage('Login successful! Redirecting...');\n        setTimeout(() => {\n          navigate('/dashboard');\n        }, 1000);\n      } else {\n        setMessage(result.message);\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-card\">\n        <h2 className=\"login-title\">Login</h2>\n        \n        {message && (\n          <div className={`${message.includes('successful') ? 'success-message' : 'error-message'}`}>\n            {message}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"form-group\">\n            <label htmlFor=\"email\" className=\"form-label\">\n              Email\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              className={`form-input ${errors.email ? 'error' : ''}`}\n              placeholder=\"Enter your email\"\n            />\n            {errors.email && (\n              <div className=\"error-message\">\n                {errors.email}\n              </div>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              Password\n            </label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              className={`form-input ${errors.password ? 'error' : ''}`}\n              placeholder=\"Enter your password\"\n            />\n            {errors.password && (\n              <div className=\"error-message\">\n                {errors.password}\n              </div>\n            )}\n          </div>\n\n          <button\n            type=\"submit\"\n            className=\"login-button\"\n            disabled={loading}\n          >\n            {loading && <span className=\"loading-spinner\"></span>}\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n\n        <Link to=\"/register\" className=\"auth-link\">\n          Don't have an account? Register Now\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAEkB;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3B,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIX,MAAM,CAACU,IAAI,CAAC,EAAE;MAChBT,SAAS,CAACY,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACnB,QAAQ,CAACE,KAAK,CAACkB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACjB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACmB,IAAI,CAACrB,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC/CiB,SAAS,CAACjB,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBgB,SAAS,CAAChB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,OAAOgB,SAAS;EAClB,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAElB,MAAMC,UAAU,GAAGN,YAAY,CAAC,CAAC;IACjC,IAAIO,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACtCtB,SAAS,CAACmB,UAAU,CAAC;MACrB;IACF;IAEAjB,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdJ,SAAS,CAAC,CAAC,CAAC,CAAC;IAEb,IAAI;MACF,MAAMuB,MAAM,GAAG,MAAMlB,KAAK,CAACV,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAE7D,IAAIyB,MAAM,CAACC,OAAO,EAAE;QAClBpB,UAAU,CAAC,kCAAkC,CAAC;QAC9CqB,UAAU,CAAC,MAAM;UACfnB,QAAQ,CAAC,YAAY,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLF,UAAU,CAACmB,MAAM,CAACpB,OAAO,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdtB,UAAU,CAAC,iDAAiD,CAAC;IAC/D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKmC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BpC,OAAA;MAAKmC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBpC,OAAA;QAAImC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAErC7B,OAAO,iBACNX,OAAA;QAAKmC,SAAS,EAAE,GAAGxB,OAAO,CAAC8B,QAAQ,CAAC,YAAY,CAAC,GAAG,iBAAiB,GAAG,eAAe,EAAG;QAAAL,QAAA,EACvFzB;MAAO;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,eAEDxC,OAAA;QAAM0C,QAAQ,EAAEjB,YAAa;QAAAW,QAAA,gBAC3BpC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAO2C,OAAO,EAAC,OAAO;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA;YACE4C,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACV5B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEf,QAAQ,CAACE,KAAM;YACtByC,QAAQ,EAAE/B,YAAa;YACvBoB,SAAS,EAAE,cAAc5B,MAAM,CAACF,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YACvD0C,WAAW,EAAC;UAAkB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACDjC,MAAM,CAACF,KAAK,iBACXL,OAAA;YAAKmC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B7B,MAAM,CAACF;UAAK;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAO2C,OAAO,EAAC,UAAU;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA;YACE4C,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACb5B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEf,QAAQ,CAACG,QAAS;YACzBwC,QAAQ,EAAE/B,YAAa;YACvBoB,SAAS,EAAE,cAAc5B,MAAM,CAACD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC1DyC,WAAW,EAAC;UAAqB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACDjC,MAAM,CAACD,QAAQ,iBACdN,OAAA;YAAKmC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B7B,MAAM,CAACD;UAAQ;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxC,OAAA;UACE4C,IAAI,EAAC,QAAQ;UACbT,SAAS,EAAC,cAAc;UACxBa,QAAQ,EAAEvC,OAAQ;UAAA2B,QAAA,GAEjB3B,OAAO,iBAAIT,OAAA;YAAMmC,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACpD/B,OAAO,GAAG,eAAe,GAAG,OAAO;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPxC,OAAA,CAACJ,IAAI;QAACqD,EAAE,EAAC,WAAW;QAACd,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA/IID,KAAK;EAAA,QASSH,OAAO,EACRD,WAAW;AAAA;AAAAqD,EAAA,GAVxBjD,KAAK;AAiJX,eAAeA,KAAK;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}