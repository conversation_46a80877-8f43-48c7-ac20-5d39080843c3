{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\React Assignment\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        backgroundColor: '#f8f9fa'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '10px',\n          fontSize: '18px',\n          color: '#666666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          style: {\n            display: 'inline-block',\n            width: '20px',\n            height: '20px',\n            border: '3px solid #cccccc',\n            borderRadius: '50%',\n            borderTopColor: '#4A90E2',\n            animation: 'spin 1s ease-in-out infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), \"Loading...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"fNj96oVmPd4sFazcimgf9N7S8ao=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "isAuthenticated", "loading", "location", "style", "display", "justifyContent", "alignItems", "height", "backgroundColor", "gap", "fontSize", "color", "className", "width", "border", "borderRadius", "borderTopColor", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/React Assignment/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst ProtectedRoute = ({ children }) => {\n  const { isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        backgroundColor: '#f8f9fa'\n      }}>\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '10px',\n          fontSize: '18px',\n          color: '#666666'\n        }}>\n          <div className=\"loading-spinner\" style={{\n            display: 'inline-block',\n            width: '20px',\n            height: '20px',\n            border: '3px solid #cccccc',\n            borderRadius: '50%',\n            borderTopColor: '#4A90E2',\n            animation: 'spin 1s ease-in-out infinite'\n          }}></div>\n          Loading...\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC9C,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,IAAIQ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKO,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,eAAe,EAAE;MACnB,CAAE;MAAAV,QAAA,eACAF,OAAA;QAAKO,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBG,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE;QACT,CAAE;QAAAb,QAAA,gBACAF,OAAA;UAAKgB,SAAS,EAAC,iBAAiB;UAACT,KAAK,EAAE;YACtCC,OAAO,EAAE,cAAc;YACvBS,KAAK,EAAE,MAAM;YACbN,MAAM,EAAE,MAAM;YACdO,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,cAAc,EAAE,SAAS;YACzBC,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,cAEX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACrB,eAAe,EAAE;IACpB;IACA,oBAAOJ,OAAA,CAACJ,QAAQ;MAAC8B,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEtB;MAAS,CAAE;MAACuB,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,OAAOvB,QAAQ;AACjB,CAAC;AAACC,EAAA,CAzCIF,cAAc;EAAA,QACmBH,OAAO,EAC3BD,WAAW;AAAA;AAAAiC,EAAA,GAFxB7B,cAAc;AA2CpB,eAAeA,cAAc;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}