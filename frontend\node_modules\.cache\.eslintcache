[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\Dashboard.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\Register.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\context\\AuthContext.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\ProtectedRoute.js": "8"}, {"size": 535, "mtime": 1751892812855, "results": "9", "hashOfConfig": "10"}, {"size": 362, "mtime": 1751892813037, "results": "11", "hashOfConfig": "10"}, {"size": 2028, "mtime": 1751893090494, "results": "12", "hashOfConfig": "10"}, {"size": 6026, "mtime": 1751893065915, "results": "13", "hashOfConfig": "10"}, {"size": 6346, "mtime": 1751893016143, "results": "14", "hashOfConfig": "10"}, {"size": 3914, "mtime": 1751892947046, "results": "15", "hashOfConfig": "10"}, {"size": 3643, "mtime": 1751892878461, "results": "16", "hashOfConfig": "10"}, {"size": 1239, "mtime": 1751893075674, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qr3z1", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\Register.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\React Assignment\\frontend\\src\\components\\ProtectedRoute.js", [], []]