{"name": "backend", "version": "1.0.0", "description": "Backend API for MERN Authentication System", "main": "server.js", "scripts": {"start": "node simple-server.js", "dev": "nodemon simple-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mern", "authentication", "jwt", "mongodb"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1"}, "devDependencies": {"nodemon": "^3.1.10"}}