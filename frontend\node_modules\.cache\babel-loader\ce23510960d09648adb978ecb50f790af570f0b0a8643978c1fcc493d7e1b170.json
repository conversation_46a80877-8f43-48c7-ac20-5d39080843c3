{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\React Assignment\\\\frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/Table.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [tableData, setTableData] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Dummy data for the table\n  useEffect(() => {\n    // Simulate API call delay\n    const timer = setTimeout(() => {\n      setTableData([{\n        id: 1,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        department: 'Engineering',\n        position: 'Senior Developer',\n        joinDate: '2022-01-15',\n        status: 'Active'\n      }, {\n        id: 2,\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        department: 'Design',\n        position: 'UI/UX Designer',\n        joinDate: '2022-03-20',\n        status: 'Active'\n      }, {\n        id: 3,\n        name: 'Mike <PERSON>',\n        email: '<EMAIL>',\n        department: 'Marketing',\n        position: 'Marketing Manager',\n        joinDate: '2021-11-10',\n        status: 'Inactive'\n      }, {\n        id: 4,\n        name: 'Sarah <PERSON>',\n        email: '<EMAIL>',\n        department: 'HR',\n        position: 'HR Specialist',\n        joinDate: '2023-02-01',\n        status: 'Active'\n      }, {\n        id: 5,\n        name: 'David Brown',\n        email: '<EMAIL>',\n        department: 'Engineering',\n        position: 'DevOps Engineer',\n        joinDate: '2022-08-15',\n        status: 'Pending'\n      }, {\n        id: 6,\n        name: 'Lisa Garcia',\n        email: '<EMAIL>',\n        department: 'Sales',\n        position: 'Sales Representative',\n        joinDate: '2023-01-10',\n        status: 'Active'\n      }, {\n        id: 7,\n        name: 'Tom Anderson',\n        email: '<EMAIL>',\n        department: 'Finance',\n        position: 'Financial Analyst',\n        joinDate: '2022-05-30',\n        status: 'Active'\n      }, {\n        id: 8,\n        name: 'Emily Davis',\n        email: '<EMAIL>',\n        department: 'Design',\n        position: 'Graphic Designer',\n        joinDate: '2023-03-15',\n        status: 'Pending'\n      }]);\n      setLoading(false);\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n  const handleLogout = () => {\n    logout();\n  };\n  const formatDate = dateString => {\n    const options = {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n  const getStatusBadgeClass = status => {\n    switch (status.toLowerCase()) {\n      case 'active':\n        return 'status-badge status-active';\n      case 'inactive':\n        return 'status-badge status-inactive';\n      case 'pending':\n        return 'status-badge status-pending';\n      default:\n        return 'status-badge';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"header-title\",\n            children: \"Employee Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"welcome-text\",\n              children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.name, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"logout-button\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), \"Loading employee data...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"table-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"header-title\",\n          children: \"Employee Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"welcome-text\",\n            children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"logout-button\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-content\",\n      children: tableData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Data Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"There are no employees to display at this time.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"data-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"table-header-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"Department\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"Position\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"Join Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"table-header-cell\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tableData.map(employee => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"table-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: employee.id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: employee.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: employee.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: employee.department\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: employee.position\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: formatDate(employee.joinDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"table-cell\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: getStatusBadgeClass(employee.status),\n                children: employee.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this)]\n          }, employee.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"8nP8luQQaq7a4kq2nH/PKFNaoPg=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "logout", "tableData", "setTableData", "loading", "setLoading", "timer", "setTimeout", "id", "name", "email", "department", "position", "joinDate", "status", "clearTimeout", "handleLogout", "formatDate", "dateString", "options", "year", "month", "day", "Date", "toLocaleDateString", "undefined", "getStatusBadgeClass", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "employee", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/React Assignment/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/Table.css';\n\nconst Dashboard = () => {\n  const { user, logout } = useAuth();\n  const [tableData, setTableData] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Dummy data for the table\n  useEffect(() => {\n    // Simulate API call delay\n    const timer = setTimeout(() => {\n      setTableData([\n        {\n          id: 1,\n          name: '<PERSON>',\n          email: '<EMAIL>',\n          department: 'Engineering',\n          position: 'Senior Developer',\n          joinDate: '2022-01-15',\n          status: 'Active'\n        },\n        {\n          id: 2,\n          name: '<PERSON>',\n          email: '<EMAIL>',\n          department: 'Design',\n          position: 'UI/UX Designer',\n          joinDate: '2022-03-20',\n          status: 'Active'\n        },\n        {\n          id: 3,\n          name: '<PERSON>',\n          email: '<EMAIL>',\n          department: 'Marketing',\n          position: 'Marketing Manager',\n          joinDate: '2021-11-10',\n          status: 'Inactive'\n        },\n        {\n          id: 4,\n          name: '<PERSON>',\n          email: '<EMAIL>',\n          department: 'HR',\n          position: 'HR Specialist',\n          joinDate: '2023-02-01',\n          status: 'Active'\n        },\n        {\n          id: 5,\n          name: 'David Brown',\n          email: '<EMAIL>',\n          department: 'Engineering',\n          position: 'DevOps Engineer',\n          joinDate: '2022-08-15',\n          status: 'Pending'\n        },\n        {\n          id: 6,\n          name: 'Lisa Garcia',\n          email: '<EMAIL>',\n          department: 'Sales',\n          position: 'Sales Representative',\n          joinDate: '2023-01-10',\n          status: 'Active'\n        },\n        {\n          id: 7,\n          name: 'Tom Anderson',\n          email: '<EMAIL>',\n          department: 'Finance',\n          position: 'Financial Analyst',\n          joinDate: '2022-05-30',\n          status: 'Active'\n        },\n        {\n          id: 8,\n          name: 'Emily Davis',\n          email: '<EMAIL>',\n          department: 'Design',\n          position: 'Graphic Designer',\n          joinDate: '2023-03-15',\n          status: 'Pending'\n        }\n      ]);\n      setLoading(false);\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  const formatDate = (dateString) => {\n    const options = { year: 'numeric', month: 'short', day: 'numeric' };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n\n  const getStatusBadgeClass = (status) => {\n    switch (status.toLowerCase()) {\n      case 'active':\n        return 'status-badge status-active';\n      case 'inactive':\n        return 'status-badge status-inactive';\n      case 'pending':\n        return 'status-badge status-pending';\n      default:\n        return 'status-badge';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"table-container\">\n        <div className=\"table-header\">\n          <div className=\"header-content\">\n            <h1 className=\"header-title\">Employee Dashboard</h1>\n            <div className=\"user-info\">\n              <p className=\"welcome-text\">Welcome, {user?.name}!</p>\n              <button onClick={handleLogout} className=\"logout-button\">\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n        <div className=\"loading-container\">\n          <div className=\"loading-text\">\n            <span className=\"loading-spinner\"></span>\n            Loading employee data...\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"table-container\">\n      <div className=\"table-header\">\n        <div className=\"header-content\">\n          <h1 className=\"header-title\">Employee Dashboard</h1>\n          <div className=\"user-info\">\n            <p className=\"welcome-text\">Welcome, {user?.name}!</p>\n            <button onClick={handleLogout} className=\"logout-button\">\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"table-content\">\n        {tableData.length === 0 ? (\n          <div className=\"empty-state\">\n            <h3>No Data Available</h3>\n            <p>There are no employees to display at this time.</p>\n          </div>\n        ) : (\n          <table className=\"data-table\">\n            <thead>\n              <tr className=\"table-header-row\">\n                <th className=\"table-header-cell\">ID</th>\n                <th className=\"table-header-cell\">Name</th>\n                <th className=\"table-header-cell\">Email</th>\n                <th className=\"table-header-cell\">Department</th>\n                <th className=\"table-header-cell\">Position</th>\n                <th className=\"table-header-cell\">Join Date</th>\n                <th className=\"table-header-cell\">Status</th>\n              </tr>\n            </thead>\n            <tbody>\n              {tableData.map((employee) => (\n                <tr key={employee.id} className=\"table-row\">\n                  <td className=\"table-cell\">{employee.id}</td>\n                  <td className=\"table-cell\">{employee.name}</td>\n                  <td className=\"table-cell\">{employee.email}</td>\n                  <td className=\"table-cell\">{employee.department}</td>\n                  <td className=\"table-cell\">{employee.position}</td>\n                  <td className=\"table-cell\">{formatDate(employee.joinDate)}</td>\n                  <td className=\"table-cell\">\n                    <span className={getStatusBadgeClass(employee.status)}>\n                      {employee.status}\n                    </span>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGN,OAAO,CAAC,CAAC;EAClC,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd;IACA,MAAMY,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BJ,YAAY,CAAC,CACX;QACEK,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,sBAAsB;QAC7BC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE,kBAAkB;QAC5BC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,wBAAwB;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,gBAAgB;QAC1BC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,0BAA0B;QACjCC,UAAU,EAAE,WAAW;QACvBC,QAAQ,EAAE,mBAAmB;QAC7BC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,0BAA0B;QACjCC,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,yBAAyB;QAChCC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE,iBAAiB;QAC3BC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,yBAAyB;QAChCC,UAAU,EAAE,OAAO;QACnBC,QAAQ,EAAE,sBAAsB;QAChCC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,0BAA0B;QACjCC,UAAU,EAAE,SAAS;QACrBC,QAAQ,EAAE,mBAAmB;QAC7BC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,yBAAyB;QAChCC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,kBAAkB;QAC5BC,QAAQ,EAAE,YAAY;QACtBC,MAAM,EAAE;MACV,CAAC,CACF,CAAC;MACFT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMU,YAAY,CAACT,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBf,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMgB,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,OAAO,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAU,CAAC;IACnE,OAAO,IAAIC,IAAI,CAACL,UAAU,CAAC,CAACM,kBAAkB,CAACC,SAAS,EAAEN,OAAO,CAAC;EACpE,CAAC;EAED,MAAMO,mBAAmB,GAAIZ,MAAM,IAAK;IACtC,QAAQA,MAAM,CAACa,WAAW,CAAC,CAAC;MAC1B,KAAK,QAAQ;QACX,OAAO,4BAA4B;MACrC,KAAK,UAAU;QACb,OAAO,8BAA8B;MACvC,KAAK,SAAS;QACZ,OAAO,6BAA6B;MACtC;QACE,OAAO,cAAc;IACzB;EACF,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK+B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BhC,OAAA;QAAK+B,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BhC,OAAA;UAAK+B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhC,OAAA;YAAI+B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDpC,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhC,OAAA;cAAG+B,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAC,WAAS,EAAC7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,EAAC,GAAC;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtDpC,OAAA;cAAQqC,OAAO,EAAElB,YAAa;cAACY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpC,OAAA;QAAK+B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChChC,OAAA;UAAK+B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhC,OAAA;YAAM+B,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4BAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BhC,OAAA;MAAK+B,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BhC,OAAA;QAAK+B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhC,OAAA;UAAI+B,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDpC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA;YAAG+B,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAC,WAAS,EAAC7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,EAAC,GAAC;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtDpC,OAAA;YAAQqC,OAAO,EAAElB,YAAa;YAACY,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3B3B,SAAS,CAACiC,MAAM,KAAK,CAAC,gBACrBtC,OAAA;QAAK+B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhC,OAAA;UAAAgC,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BpC,OAAA;UAAAgC,QAAA,EAAG;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,gBAENpC,OAAA;QAAO+B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAC3BhC,OAAA;UAAAgC,QAAA,eACEhC,OAAA;YAAI+B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC9BhC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCpC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CpC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CpC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDpC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CpC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDpC,OAAA;cAAI+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRpC,OAAA;UAAAgC,QAAA,EACG3B,SAAS,CAACkC,GAAG,CAAEC,QAAQ,iBACtBxC,OAAA;YAAsB+B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzChC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,QAAQ,CAAC7B;YAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7CpC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,QAAQ,CAAC5B;YAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/CpC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,QAAQ,CAAC3B;YAAK;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChDpC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,QAAQ,CAAC1B;YAAU;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDpC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,QAAQ,CAACzB;YAAQ;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDpC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEZ,UAAU,CAACoB,QAAQ,CAACxB,QAAQ;YAAC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/DpC,OAAA;cAAI+B,SAAS,EAAC,YAAY;cAAAC,QAAA,eACxBhC,OAAA;gBAAM+B,SAAS,EAAEF,mBAAmB,CAACW,QAAQ,CAACvB,MAAM,CAAE;gBAAAe,QAAA,EACnDQ,QAAQ,CAACvB;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GAXEI,QAAQ,CAAC7B,EAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYhB,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CA9LID,SAAS;EAAA,QACYH,OAAO;AAAA;AAAA2C,EAAA,GAD5BxC,SAAS;AAgMf,eAAeA,SAAS;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}