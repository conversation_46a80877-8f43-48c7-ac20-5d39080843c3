/* Protected Table Page Styles */
.table-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.table-header {
  background-color: #457B9D;
  color: #F1FAEE;
  padding: 20px 0;
  margin-bottom: 30px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 28px;
  font-weight: bold;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.welcome-text {
  font-size: 16px;
  margin: 0;
}

.logout-button {
  background-color: #F1FAEE;
  color: #457B9D;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: #e8f4f8;
  transform: translateY(-1px);
}

.table-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table-header-row {
  background-color: #A8DADC;
}

.table-header-cell {
  padding: 16px 20px;
  text-align: left;
  font-weight: bold;
  color: #1D3557;
  font-size: 16px;
  border-bottom: 2px solid #BDBDBD;
}

.table-row {
  transition: background-color 0.2s ease;
}

.table-row:nth-child(even) {
  background-color: #F1FAEE;
}

.table-row:nth-child(odd) {
  background-color: #E0FBFC;
}

.table-row:hover {
  background-color: #d4edda;
}

.table-cell {
  padding: 14px 20px;
  color: #333333;
  font-size: 14px;
  border-bottom: 1px solid #BDBDBD;
}

.table-cell:first-child {
  font-weight: 600;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

/* Loading state */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 18px;
  color: #666666;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666666;
}

.empty-state h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #333333;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .user-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .table-content {
    padding: 0 10px;
  }
  
  .data-table {
    font-size: 12px;
  }
  
  .table-header-cell,
  .table-cell {
    padding: 10px 8px;
  }
  
  .header-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .data-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .table-header-cell,
  .table-cell {
    min-width: 120px;
  }
}
