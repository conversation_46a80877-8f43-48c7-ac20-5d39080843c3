{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\React Assignment\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport Dashboard from './components/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport './App.css';\n\n// Component to handle root route redirection\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RootRedirect = () => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        backgroundColor: '#f8f9fa'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '10px',\n          fontSize: '18px',\n          color: '#666666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          style: {\n            display: 'inline-block',\n            width: '20px',\n            height: '20px',\n            border: '3px solid #cccccc',\n            borderRadius: '50%',\n            borderTopColor: '#4A90E2',\n            animation: 'spin 1s ease-in-out infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), \"Loading...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: isAuthenticated ? \"/dashboard\" : \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 10\n  }, this);\n};\n_s(RootRedirect, \"F3aPsg481KjBH7Z7iYl6LJifZz0=\", false, function () {\n  return [useAuth];\n});\n_c = RootRedirect;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(RootRedirect, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"RootRedirect\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "Register", "Dashboard", "ProtectedRoute", "jsxDEV", "_jsxDEV", "RootRedirect", "_s", "isAuthenticated", "loading", "style", "display", "justifyContent", "alignItems", "height", "backgroundColor", "children", "gap", "fontSize", "color", "className", "width", "border", "borderRadius", "borderTopColor", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "App", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/React Assignment/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport Dashboard from './components/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport './App.css';\n\n// Component to handle root route redirection\nconst RootRedirect = () => {\n  const { isAuthenticated, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        backgroundColor: '#f8f9fa'\n      }}>\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '10px',\n          fontSize: '18px',\n          color: '#666666'\n        }}>\n          <div className=\"loading-spinner\" style={{\n            display: 'inline-block',\n            width: '20px',\n            height: '20px',\n            border: '3px solid #cccccc',\n            borderRadius: '50%',\n            borderTopColor: '#4A90E2',\n            animation: 'spin 1s ease-in-out infinite'\n          }}></div>\n          Loading...\n        </div>\n      </div>\n    );\n  }\n\n  return <Navigate to={isAuthenticated ? \"/dashboard\" : \"/login\"} replace />;\n};\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<RootRedirect />} />\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route\n              path=\"/dashboard\"\n              element={\n                <ProtectedRoute>\n                  <Dashboard />\n                </ProtectedRoute>\n              }\n            />\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EAE9C,IAAIU,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKK,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,eAAe,EAAE;MACnB,CAAE;MAAAC,QAAA,eACAX,OAAA;QAAKK,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE;QACT,CAAE;QAAAH,QAAA,gBACAX,OAAA;UAAKe,SAAS,EAAC,iBAAiB;UAACV,KAAK,EAAE;YACtCC,OAAO,EAAE,cAAc;YACvBU,KAAK,EAAE,MAAM;YACbP,MAAM,EAAE,MAAM;YACdQ,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,cAAc,EAAE,SAAS;YACzBC,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,cAEX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBAAOxB,OAAA,CAACR,QAAQ;IAACiC,EAAE,EAAEtB,eAAe,GAAG,YAAY,GAAG,QAAS;IAACuB,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC5E,CAAC;AAACtB,EAAA,CAnCID,YAAY;EAAA,QACqBP,OAAO;AAAA;AAAAiC,EAAA,GADxC1B,YAAY;AAqClB,SAAS2B,GAAGA,CAAA,EAAG;EACb,oBACE5B,OAAA,CAACP,YAAY;IAAAkB,QAAA,eACXX,OAAA,CAACX,MAAM;MAAAsB,QAAA,eACLX,OAAA;QAAKe,SAAS,EAAC,KAAK;QAAAJ,QAAA,eAClBX,OAAA,CAACV,MAAM;UAAAqB,QAAA,gBACLX,OAAA,CAACT,KAAK;YAACsC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAE9B,OAAA,CAACC,YAAY;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CxB,OAAA,CAACT,KAAK;YAACsC,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAE9B,OAAA,CAACL,KAAK;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CxB,OAAA,CAACT,KAAK;YAACsC,IAAI,EAAC,WAAW;YAACC,OAAO,eAAE9B,OAAA,CAACJ,QAAQ;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDxB,OAAA,CAACT,KAAK;YACJsC,IAAI,EAAC,YAAY;YACjBC,OAAO,eACL9B,OAAA,CAACF,cAAc;cAAAa,QAAA,eACbX,OAAA,CAACH,SAAS;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxB,OAAA,CAACT,KAAK;YAACsC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAE9B,OAAA,CAACR,QAAQ;cAACiC,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACO,GAAA,GAvBQH,GAAG;AAyBZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}