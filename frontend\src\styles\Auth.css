/* Registration Form Styles */
.register-container {
  min-height: 100vh;
  background-color: #D8E2DC;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.register-card {
  background-color: #FFFFFF;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
}

.register-title {
  font-size: 28px;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 30px;
}

/* Login Form Styles */
.login-container {
  min-height: 100vh;
  background-color: #EDF6F9;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-card {
  background-color: #FFFFFF;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
}

.login-title {
  font-size: 28px;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 30px;
}

/* Common Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: #666666;
  font-weight: 500;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #CCCCCC;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #4A90E2;
}

.form-input.error {
  border-color: #e74c3c;
}

/* Button Styles */
.register-button {
  width: 100%;
  padding: 14px;
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 10px;
}

.register-button:hover {
  background-color: #357ABD;
}

.register-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.login-button {
  width: 100%;
  padding: 14px;
  background-color: #118AB2;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 10px;
}

.login-button:hover {
  background-color: #0e7a9a;
}

.login-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Link Styles */
.auth-link {
  display: block;
  text-align: center;
  margin-top: 20px;
  color: #0077B6;
  text-decoration: none;
  font-size: 14px;
}

.auth-link:hover {
  text-decoration: underline;
}

/* Error and Success Messages */
.error-message {
  background-color: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  border: 1px solid #fcc;
}

.success-message {
  background-color: #efe;
  color: #363;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  border: 1px solid #cfc;
}

.error-list {
  margin: 0;
  padding-left: 20px;
}

.error-list li {
  margin-bottom: 5px;
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .register-card,
  .login-card {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .register-title,
  .login-title {
    font-size: 24px;
  }
}
