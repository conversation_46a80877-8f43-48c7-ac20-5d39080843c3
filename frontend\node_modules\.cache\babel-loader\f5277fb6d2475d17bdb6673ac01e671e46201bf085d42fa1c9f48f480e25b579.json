{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\React Assignment\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n\n  // Set up axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const storedToken = localStorage.getItem('token');\n      const storedUser = localStorage.getItem('user');\n      if (storedToken && storedUser) {\n        try {\n          setToken(storedToken);\n          setUser(JSON.parse(storedUser));\n          axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/login', {\n        email,\n        password\n      });\n      if (response.data.success) {\n        const {\n          token,\n          user\n        } = response.data;\n\n        // Store in localStorage\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n\n        // Update state\n        setToken(token);\n        setUser(user);\n\n        // Set axios default header\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        return {\n          success: true,\n          message: response.data.message\n        };\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/register', userData);\n      if (response.data.success) {\n        const {\n          token,\n          user\n        } = response.data;\n\n        // Store in localStorage\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n\n        // Update state\n        setToken(token);\n        setUser(user);\n\n        // Set axios default header\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        return {\n          success: true,\n          message: response.data.message\n        };\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data, _error$response3, _error$response3$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed';\n      const errors = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.errors) || [];\n      return {\n        success: false,\n        message,\n        errors\n      };\n    }\n  };\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Remove axios default header\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    isAuthenticated: !!token && !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"FXi2zprQ4FdDQp5Badf2jWUNTs8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "defaults", "headers", "common", "checkAuth", "storedToken", "storedUser", "JSON", "parse", "error", "console", "logout", "login", "email", "password", "response", "post", "data", "success", "setItem", "stringify", "message", "_error$response", "_error$response$data", "register", "userData", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "errors", "removeItem", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/React Assignment/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n\n  // Set up axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const storedToken = localStorage.getItem('token');\n      const storedUser = localStorage.getItem('user');\n      \n      if (storedToken && storedUser) {\n        try {\n          setToken(storedToken);\n          setUser(JSON.parse(storedUser));\n          axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/login', {\n        email,\n        password\n      });\n\n      if (response.data.success) {\n        const { token, user } = response.data;\n        \n        // Store in localStorage\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n        \n        // Update state\n        setToken(token);\n        setUser(user);\n        \n        // Set axios default header\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        \n        return { success: true, message: response.data.message };\n      }\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed';\n      return { success: false, message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/register', userData);\n\n      if (response.data.success) {\n        const { token, user } = response.data;\n        \n        // Store in localStorage\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n        \n        // Update state\n        setToken(token);\n        setUser(user);\n        \n        // Set axios default header\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        \n        return { success: true, message: response.data.message };\n      }\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      const errors = error.response?.data?.errors || [];\n      return { success: false, message, errors };\n    }\n  };\n\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    \n    // Clear state\n    setToken(null);\n    setUser(null);\n    \n    // Remove axios default header\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    isAuthenticated: !!token && !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAACkB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EACjE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAE,SAAS,CAAC,MAAM;IACd,IAAIc,KAAK,EAAE;MACTb,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUR,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOb,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACR,KAAK,CAAC,CAAC;;EAEX;EACAd,SAAS,CAAC,MAAM;IACd,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,WAAW,GAAGR,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MACjD,MAAMQ,UAAU,GAAGT,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE/C,IAAIO,WAAW,IAAIC,UAAU,EAAE;QAC7B,IAAI;UACFV,QAAQ,CAACS,WAAW,CAAC;UACrBX,OAAO,CAACa,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC,CAAC;UAC/BxB,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUE,WAAW,EAAE;QAC1E,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvDE,MAAM,CAAC,CAAC;QACV;MACF;MACAX,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDI,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,IAAI,CAAC,iCAAiC,EAAE;QACnEH,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEvB,KAAK;UAAEF;QAAK,CAAC,GAAGsB,QAAQ,CAACE,IAAI;;QAErC;QACApB,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAExB,KAAK,CAAC;QACpCE,YAAY,CAACsB,OAAO,CAAC,MAAM,EAAEZ,IAAI,CAACa,SAAS,CAAC3B,IAAI,CAAC,CAAC;;QAElD;QACAG,QAAQ,CAACD,KAAK,CAAC;QACfD,OAAO,CAACD,IAAI,CAAC;;QAEb;QACAX,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUR,KAAK,EAAE;QAElE,OAAO;UAAEuB,OAAO,EAAE,IAAI;UAAEG,OAAO,EAAEN,QAAQ,CAACE,IAAI,CAACI;QAAQ,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MACd,MAAMF,OAAO,GAAG,EAAAC,eAAA,GAAAb,KAAK,CAACM,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBL,IAAI,cAAAM,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,cAAc;MAC/D,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,IAAI,CAAC,oCAAoC,EAAES,QAAQ,CAAC;MAEjF,IAAIV,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEvB,KAAK;UAAEF;QAAK,CAAC,GAAGsB,QAAQ,CAACE,IAAI;;QAErC;QACApB,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAExB,KAAK,CAAC;QACpCE,YAAY,CAACsB,OAAO,CAAC,MAAM,EAAEZ,IAAI,CAACa,SAAS,CAAC3B,IAAI,CAAC,CAAC;;QAElD;QACAG,QAAQ,CAACD,KAAK,CAAC;QACfD,OAAO,CAACD,IAAI,CAAC;;QAEb;QACAX,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUR,KAAK,EAAE;QAElE,OAAO;UAAEuB,OAAO,EAAE,IAAI;UAAEG,OAAO,EAAEN,QAAQ,CAACE,IAAI,CAACI;QAAQ,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,MAAMR,OAAO,GAAG,EAAAK,gBAAA,GAAAjB,KAAK,CAACM,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,qBAAqB;MACtE,MAAMS,MAAM,GAAG,EAAAF,gBAAA,GAAAnB,KAAK,CAACM,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI,EAAE;MACjD,OAAO;QAAEZ,OAAO,EAAE,KAAK;QAAEG,OAAO;QAAES;MAAO,CAAC;IAC5C;EACF,CAAC;EAED,MAAMnB,MAAM,GAAGA,CAAA,KAAM;IACnB;IACAd,YAAY,CAACkC,UAAU,CAAC,OAAO,CAAC;IAChClC,YAAY,CAACkC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAnC,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,OAAOZ,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAM6B,KAAK,GAAG;IACZvC,IAAI;IACJE,KAAK;IACLI,OAAO;IACPa,KAAK;IACLY,QAAQ;IACRb,MAAM;IACNsB,eAAe,EAAE,CAAC,CAACtC,KAAK,IAAI,CAAC,CAACF;EAChC,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACiD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAzC,QAAA,EAChCA;EAAQ;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC9C,GAAA,CAxHWF,YAAY;AAAAiD,EAAA,GAAZjD,YAAY;AAAA,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}