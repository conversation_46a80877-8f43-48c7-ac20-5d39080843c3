import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import '../styles/Table.css';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Dummy data for the table
  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      setTableData([
        {
          id: 1,
          name: '<PERSON>',
          email: '<EMAIL>',
          department: 'Engineering',
          position: 'Senior Developer',
          joinDate: '2022-01-15',
          status: 'Active'
        },
        {
          id: 2,
          name: '<PERSON>',
          email: '<EMAIL>',
          department: 'Design',
          position: 'UI/UX Designer',
          joinDate: '2022-03-20',
          status: 'Active'
        },
        {
          id: 3,
          name: '<PERSON>',
          email: '<EMAIL>',
          department: 'Marketing',
          position: 'Marketing Manager',
          joinDate: '2021-11-10',
          status: 'Inactive'
        },
        {
          id: 4,
          name: '<PERSON>',
          email: '<EMAIL>',
          department: 'HR',
          position: 'HR Specialist',
          joinDate: '2023-02-01',
          status: 'Active'
        },
        {
          id: 5,
          name: 'David Brown',
          email: '<EMAIL>',
          department: 'Engineering',
          position: 'DevOps Engineer',
          joinDate: '2022-08-15',
          status: 'Pending'
        },
        {
          id: 6,
          name: 'Lisa Garcia',
          email: '<EMAIL>',
          department: 'Sales',
          position: 'Sales Representative',
          joinDate: '2023-01-10',
          status: 'Active'
        },
        {
          id: 7,
          name: 'Tom Anderson',
          email: '<EMAIL>',
          department: 'Finance',
          position: 'Financial Analyst',
          joinDate: '2022-05-30',
          status: 'Active'
        },
        {
          id: 8,
          name: 'Emily Davis',
          email: '<EMAIL>',
          department: 'Design',
          position: 'Graphic Designer',
          joinDate: '2023-03-15',
          status: 'Pending'
        }
      ]);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleLogout = () => {
    logout();
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const getStatusBadgeClass = (status) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'status-badge status-active';
      case 'inactive':
        return 'status-badge status-inactive';
      case 'pending':
        return 'status-badge status-pending';
      default:
        return 'status-badge';
    }
  };

  if (loading) {
    return (
      <div className="table-container">
        <div className="table-header">
          <div className="header-content">
            <h1 className="header-title">Employee Dashboard</h1>
            <div className="user-info">
              <p className="welcome-text">Welcome, {user?.name}!</p>
              <button onClick={handleLogout} className="logout-button">
                Logout
              </button>
            </div>
          </div>
        </div>
        <div className="loading-container">
          <div className="loading-text">
            <span className="loading-spinner"></span>
            Loading employee data...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="table-container">
      <div className="table-header">
        <div className="header-content">
          <h1 className="header-title">Employee Dashboard</h1>
          <div className="user-info">
            <p className="welcome-text">Welcome, {user?.name}!</p>
            <button onClick={handleLogout} className="logout-button">
              Logout
            </button>
          </div>
        </div>
      </div>

      <div className="table-content">
        {tableData.length === 0 ? (
          <div className="empty-state">
            <h3>No Data Available</h3>
            <p>There are no employees to display at this time.</p>
          </div>
        ) : (
          <table className="data-table">
            <thead>
              <tr className="table-header-row">
                <th className="table-header-cell">ID</th>
                <th className="table-header-cell">Name</th>
                <th className="table-header-cell">Email</th>
                <th className="table-header-cell">Department</th>
                <th className="table-header-cell">Position</th>
                <th className="table-header-cell">Join Date</th>
                <th className="table-header-cell">Status</th>
              </tr>
            </thead>
            <tbody>
              {tableData.map((employee) => (
                <tr key={employee.id} className="table-row">
                  <td className="table-cell">{employee.id}</td>
                  <td className="table-cell">{employee.name}</td>
                  <td className="table-cell">{employee.email}</td>
                  <td className="table-cell">{employee.department}</td>
                  <td className="table-cell">{employee.position}</td>
                  <td className="table-cell">{formatDate(employee.joinDate)}</td>
                  <td className="table-cell">
                    <span className={getStatusBadgeClass(employee.status)}>
                      {employee.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
