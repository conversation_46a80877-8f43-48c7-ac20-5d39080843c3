{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport toFormData from './toFormData.js';\nimport platform from '../platform/index.js';\nexport default function toURLEncodedForm(data, options) {\n  return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({\n    visitor: function (value, key, path, helpers) {\n      if (platform.isNode && utils.isBuffer(value)) {\n        this.append(key, value.toString('base64'));\n        return false;\n      }\n      return helpers.defaultVisitor.apply(this, arguments);\n    }\n  }, options));\n}", "map": {"version": 3, "names": ["utils", "toFormData", "platform", "toURLEncodedForm", "data", "options", "classes", "URLSearchParams", "Object", "assign", "visitor", "value", "key", "path", "helpers", "isNode", "<PERSON><PERSON><PERSON><PERSON>", "append", "toString", "defaultVisitor", "apply", "arguments"], "sources": ["C:/Users/<USER>/node_modules/axios/lib/helpers/toURLEncodedForm.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport toFormData from './toFormData.js';\nimport platform from '../platform/index.js';\n\nexport default function toURLEncodedForm(data, options) {\n  return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({\n    visitor: function(value, key, path, helpers) {\n      if (platform.isNode && utils.isBuffer(value)) {\n        this.append(key, value.toString('base64'));\n        return false;\n      }\n\n      return helpers.defaultVisitor.apply(this, arguments);\n    }\n  }, options));\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,sBAAsB;AAE3C,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACtD,OAAOJ,UAAU,CAACG,IAAI,EAAE,IAAIF,QAAQ,CAACI,OAAO,CAACC,eAAe,CAAC,CAAC,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC5EC,OAAO,EAAE,SAAAA,CAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAE;MAC3C,IAAIZ,QAAQ,CAACa,MAAM,IAAIf,KAAK,CAACgB,QAAQ,CAACL,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACM,MAAM,CAACL,GAAG,EAAED,KAAK,CAACO,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,KAAK;MACd;MAEA,OAAOJ,OAAO,CAACK,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACtD;EACF,CAAC,EAAEhB,OAAO,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}